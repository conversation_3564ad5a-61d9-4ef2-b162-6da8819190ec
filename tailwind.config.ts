
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				
				// KoGidi brand colors
				kogidi: {
					dark: '#1E4B68',   // Main wordmark
					light: '#CADAD1',  // Soft background fill
					teal: '#6D9F9A',   // Secondary panels/buttons
					green: '#1B9655',  // Brand buttons, success accents
					gold: '#EBA237',   // CTAs, warning highlights
					red: '#D84545',    // Error messages, urgent alerts
				},
				
				primary: {
					DEFAULT: '#1B9655', // Green as primary
					foreground: '#ffffff',
					50: '#e8f5ee',
					100: '#c8e6d5',
					200: '#a5d6b9',
					300: '#81c79d',
					400: '#5db880',
					500: '#1B9655',
					600: '#188c4f',
					700: '#147a45',
					800: '#0f683b',
					900: '#095631',
				},
				secondary: {
					DEFAULT: '#6D9F9A',
					foreground: '#ffffff',
					50: '#edf3f3',
					100: '#d1e3e1',
					200: '#b3d2ce',
					300: '#94c1bb',
					400: '#6D9F9A',
					500: '#5c8e89',
					600: '#4c7d78',
					700: '#3d6c67',
					800: '#345b56',
					900: '#2a4945',
				},
				accent: {
					DEFAULT: '#EBA237',
					foreground: '#1E4B68',
					50: '#fef6eb',
					100: '#fdebd0',
					200: '#f7d7a5',
					300: '#f2c27a',
					400: '#EBA237',
					500: '#e58e23',
					600: '#d47a1a',
					700: '#bd6613',
					800: '#9a5211',
					900: '#77400e',
				},
				destructive: {
					DEFAULT: '#D84545',
					foreground: '#ffffff',
					50: '#fbeaea',
					100: '#f5cccc',
					200: '#ee9d9d',
					300: '#e76f6f',
					400: '#D84545',
					500: '#c33636',
					600: '#ad292a',
					700: '#971f21',
					800: '#81191b',
					900: '#6b1415',
				},
				muted: {
					DEFAULT: '#CADAD1',
					foreground: '#1E4B68',
				},
				popover: {
					DEFAULT: '#ffffff',
					foreground: '#1E4B68',
				},
				card: {
					DEFAULT: '#ffffff',
					foreground: '#1E4B68',
				},
				neutral: {
					light: '#F5F5F5',
					dark: '#424242',
				},
			},
			fontFamily: {
				inter: ['Inter', 'sans-serif'],
			},
			fontSize: {
				'base': '1rem',
				'heading-xs': '1.25rem',
				'heading-sm': '1.5rem', 
				'heading-md': '1.75rem',
				'heading-lg': '1.875rem',
				'heading-xl': '2rem',
				'heading-2xl': '2.25rem',
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' },
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' },
				},
				'pulse-light': {
					'0%, 100%': { opacity: '1' },
					'50%': { opacity: '0.5' },
				},
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				'float': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-5px)' },
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'pulse-light': 'pulse-light 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
				'fade-in': 'fade-in 0.5s ease-out',
				'float': 'float 3s ease-in-out infinite',
			},
			backgroundImage: {
				'adire-pattern': "url('/src/assets/adire-pattern.svg')",
				'leaf-pattern': "url('/src/assets/leaf-pattern.svg')",
			},
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
