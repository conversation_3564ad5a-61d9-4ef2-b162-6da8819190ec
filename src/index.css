
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 151 21% 91%;
    --foreground: 203 55% 26%;

    --card: 0 0% 100%;
    --card-foreground: 203 55% 26%;

    --popover: 0 0% 100%;
    --popover-foreground: 203 55% 26%;

    --primary: 150 70% 35%;
    --primary-foreground: 0 0% 100%;

    --secondary: 174 25% 66%;
    --secondary-foreground: 0 0% 100%;

    --muted: 151 21% 91%;
    --muted-foreground: 203 55% 26%;

    --accent: 38 80% 55%;
    --accent-foreground: 203 55% 26%;

    --destructive: 0 59% 51%;
    --destructive-foreground: 0 0% 100%;

    --border: 151 15% 90%;
    --input: 151 15% 90%;
    --ring: 150 70% 35%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 203 55% 10%;
    --foreground: 151 21% 91%;

    --card: 203 55% 15%;
    --card-foreground: 151 21% 91%;

    --popover: 203 55% 15%;
    --popover-foreground: 151 21% 91%;

    --primary: 150 70% 35%;
    --primary-foreground: 0 0% 100%;

    --secondary: 174 25% 40%;
    --secondary-foreground: 0 0% 100%;

    --muted: 203 30% 25%;
    --muted-foreground: 151 10% 70%;

    --accent: 38 80% 45%;
    --accent-foreground: 203 55% 26%;

    --destructive: 0 59% 40%;
    --destructive-foreground: 0 0% 100%;

    --border: 203 30% 25%;
    --input: 203 30% 25%;
    --ring: 150 70% 35%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-kogidi-light text-kogidi-dark font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-inter font-semibold text-kogidi-dark;
  }

  h1 {
    @apply text-heading-2xl;
  }

  h2 {
    @apply text-heading-xl;
  }

  h3 {
    @apply text-heading-lg;
  }

  h4 {
    @apply text-heading-md;
  }

  h5 {
    @apply text-heading-sm;
  }

  h6 {
    @apply text-heading-xs;
  }
}

@layer utilities {
  .kogidi-card {
    @apply bg-white shadow-md rounded-lg border border-kogidi-light/60 hover:shadow-lg transition-shadow duration-300;
  }
  
  .kogidi-gradient-text {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }
  
  .kogidi-gradient-bg {
    @apply bg-gradient-to-r from-primary/80 to-secondary/80;
  }
}
