
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  language: string;
  grade: string;
  subject: string;
  isDownloaded: boolean;
  progress: number;
}

interface AppContextType {
  courses: Course[];
  downloadedCourses: Course[];
  currentCourse: Course | null;
  isVoiceAssistantActive: boolean;
  activateVoiceAssistant: () => void;
  deactivateVoiceAssistant: () => void;
  downloadCourse: (courseId: string) => void;
  openCourse: (courseId: string) => void;
  changeLanguage: (language: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Sample mock data
const mockCourses: Course[] = [
  {
    id: '1',
    title: 'Introduction to Mathematics',
    description: 'Learn basic math concepts and operations',
    thumbnail: 'https://images.unsplash.com/photo-1518133910546-b6c2fb7d79e3?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'en',
    grade: 'Primary 5',
    subject: 'Mathematics',
    isDownloaded: false,
    progress: 0,
  },
  {
    id: '2',
    title: 'English Grammar',
    description: 'Master English grammar rules and communication',
    thumbnail: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'en',
    grade: 'Primary 6',
    subject: 'English',
    isDownloaded: true,
    progress: 35,
  },
  {
    id: '3',
    title: 'Basic Science',
    description: 'Explore the world of science and technology',
    thumbnail: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'en',
    grade: 'Primary 5',
    subject: 'Science',
    isDownloaded: false,
    progress: 0,
  },
  {
    id: '4',
    title: 'Èdè Yorùbá Kékeré',
    description: 'Ẹ̀kọ́ nípa èdè àti àṣà Yorùbá',
    thumbnail: 'https://images.unsplash.com/photo-1590002893558-64f0d58dcca4?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'yo',
    grade: 'Primary 4',
    subject: 'Languages',
    isDownloaded: false,
    progress: 0,
  },
  {
    id: '5',
    title: 'Asụsụ Igbo Mmalite',
    description: 'Mụta asụsụ na omenala Igbo',
    thumbnail: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'ig',
    grade: 'Primary 4',
    subject: 'Languages',
    isDownloaded: false,
    progress: 0,
  },
  {
    id: '6',
    title: 'History of Nigeria',
    description: 'Learn about the rich history of Nigeria',
    thumbnail: 'https://images.unsplash.com/photo-1587331722574-acf78f587c4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    language: 'en',
    grade: 'Junior Secondary 1',
    subject: 'Social Studies',
    isDownloaded: true,
    progress: 75,
  }
];

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [courses, setCourses] = useState<Course[]>(mockCourses);
  const [currentCourse, setCurrentCourse] = useState<Course | null>(null);
  const [isVoiceAssistantActive, setIsVoiceAssistantActive] = useState(false);

  const downloadedCourses = courses.filter(course => course.isDownloaded);

  const downloadCourse = (courseId: string) => {
    setCourses(prevCourses => 
      prevCourses.map(course => 
        course.id === courseId ? { ...course, isDownloaded: true } : course
      )
    );

    // Simulate saving to IndexedDB
    console.log(`Course ${courseId} downloaded and saved offline`);
  };

  const openCourse = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    if (course) {
      setCurrentCourse(course);
    }
  };

  const activateVoiceAssistant = () => {
    setIsVoiceAssistantActive(true);
  };

  const deactivateVoiceAssistant = () => {
    setIsVoiceAssistantActive(false);
  };

  const changeLanguage = (language: string) => {
    // Language is now handled directly in i18n
  };

  return (
    <AppContext.Provider
      value={{
        courses,
        downloadedCourses,
        currentCourse,
        isVoiceAssistantActive,
        activateVoiceAssistant,
        deactivateVoiceAssistant,
        downloadCourse,
        openCourse,
        changeLanguage,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
