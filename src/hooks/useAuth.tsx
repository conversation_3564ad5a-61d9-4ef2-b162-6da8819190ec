import { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import api from '../services/apiClient';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';

// User types
export type UserType = 'student' | 'teacher' | 'parent';

// Authentication states
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// User interface
interface User {
  id: string;
  first_name: string;
  last_name: string;
  name?: string;
  email: string;
  userType?: UserType;
  resident_state?: string;
  is_student?: boolean;
  is_teacher?: boolean;
  is_parent?: boolean;
  profile_image?: string;
}

// Login form data
interface LoginData {
  email: string;
  password: string;
  userType: UserType;
}

// Signup form data
interface SignupData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  password2: string;
  resident_state: string;
  is_student: boolean;
  is_teacher: boolean;
  is_parent: boolean;
}

// Auth context interface
interface AuthContextType extends AuthState {
  login: (data: LoginData) => Promise<void>;
  signup: (data: SignupData, userType: UserType) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider = ({ children }: AuthProviderProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  // Auth state
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('kogidi-token');
        if (!token) {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
          return;
        }

        const response = await api.get('/api/v1/auth/me/');
        const userData = response.data;
        
        // Ensure user has a display name
        const user = {
          ...userData,
          name: userData.name || `${userData.first_name} ${userData.last_name}`.trim() || userData.email
        };

        // Store user data for persistence
        localStorage.setItem('kogidi-user', JSON.stringify(user));
        
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
        });
      } catch (error) {
        console.log('Not authenticated or failed to fetch user');
        // Clear invalid token
        localStorage.removeItem('kogidi-token');
        localStorage.removeItem('kogidi-user');
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    };

    checkAuth();
  }, []);

  // Listen for auth events (like unauthorized from interceptor)
  useEffect(() => {
    const handleUnauthorized = () => {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
    };

    window.addEventListener('auth:unauthorized', handleUnauthorized);
    
    return () => {
      window.removeEventListener('auth:unauthorized', handleUnauthorized);
    };
  }, []);

  // Login function
  const login = async (data: LoginData) => {
    try {
      const response = await api.post(`/api/v1/auth/login/?type=${data.userType}`, {
        email: data.email,
        password: data.password,
      });

      const userData = response.data;
      const token = response.data.token || response.data.access_token || response.data.access;

      // Store token and user data
      if (token) {
        localStorage.setItem('kogidi-token', token);
      }

      // Ensure user has a display name
      const user = {
        ...userData,
        name: userData.name || `${userData.first_name} ${userData.last_name}`.trim() || userData.email,
        userType: data.userType
      };

      localStorage.setItem('kogidi-user', JSON.stringify(user));

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
      });

      toast({
        title: t('auth.loginSuccess'),
        description: t('auth.welcomeBack'),
      });
      
      // Redirect to dashboard based on user type
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.status === 401) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Please check your email and password format.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      }
      
      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      
      throw error;
    }
  };

  // Signup function
  const signup = async (data: SignupData, userType: UserType) => {
    try {
      const payload = {
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        password: data.password,
        password2: data.password2,
        resident_state: data.resident_state,
        is_student: data.is_student,
        is_teacher: data.is_teacher,
        is_parent: data.is_parent
      };

      const response = await api.post(`/api/v1/auth/signup/?type=${userType}`, payload);
      const userData = response.data;
      const token = response.data.token || response.data.access_token || response.data.access;

      // Store token and user data
      if (token) {
        localStorage.setItem('kogidi-token', token);
      }

      // Ensure user has a display name
      const user = {
        ...userData,
        name: userData.name || `${userData.first_name} ${userData.last_name}`.trim() || userData.email,
        userType
      };

      localStorage.setItem('kogidi-user', JSON.stringify(user));

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
      });

      toast({
        title: t('auth.signupSuccess'),
        description: t('auth.accountCreated'),
      });
      
      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Signup error:', error);
      
      let errorMessage = 'Registration failed. Please check your information and try again.';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.email) {
        errorMessage = 'This email is already registered. Please use a different email or try logging in.';
      } else if (error.response?.data?.password) {
        errorMessage = 'Password requirements not met. Please ensure your password is strong enough.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Please check all fields are filled correctly.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      }
      
      toast({
        title: 'Registration Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await api.post('/api/v1/auth/logout/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local state and storage
      localStorage.removeItem('kogidi-token');
      localStorage.removeItem('kogidi-user');
      
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });

      toast({
        title: t('auth.logoutSuccess'),
        description: t('auth.loggedOut'),
      });
      
      // Redirect to home
      navigate('/');
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    try {
      const response = await api.get('/api/v1/auth/me/');
      const userData = response.data;
      
      // Ensure user has a display name
      const user = {
        ...userData,
        name: userData.name || `${userData.first_name} ${userData.last_name}`.trim() || userData.email
      };

      localStorage.setItem('kogidi-user', JSON.stringify(user));
      
      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to refresh user data', error);
    }
  };

  // Provide auth context
  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        signup,
        logout,
        refreshUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};
