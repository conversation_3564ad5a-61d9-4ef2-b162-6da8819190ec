
import axios from 'axios';

// API base URLs for different environments
const API_URLS = {
  development: 'https://kogidi-be.onrender.com',
  production: 'https://kogidi-be.onrender.com',
  test: 'http://localhost:8000'
};

// Get current environment
const ENV = process.env.NODE_ENV || 'development';
const BASE_URL = API_URLS[ENV as keyof typeof API_URLS];

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for auth token
api.interceptors.request.use(
  (config) => {
    // Get token from local storage if available
    const token = localStorage.getItem('kogidi-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling and token management
api.interceptors.response.use(
  (response) => {
    // Store token if received in response
    const token = response.data?.token || response.data?.access_token || response.data?.access;
    if (token) {
      localStorage.setItem('kogidi-token', token);
    }
    return response;
  },
  (error) => {
    // Handle unauthorized errors (401)
    if (error.response && error.response.status === 401) {
      // Dispatch unauthorized event
      window.dispatchEvent(new Event('auth:unauthorized'));
      
      // Clear local storage
      localStorage.removeItem('kogidi-token');
      localStorage.removeItem('kogidi-user');
    }
    
    return Promise.reject(error);
  }
);

export default api;
