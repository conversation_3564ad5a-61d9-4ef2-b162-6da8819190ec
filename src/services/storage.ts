
// Storage Service for handling offline data persistence

// Check if IndexedDB is available
const isIndexedDBAvailable = () => {
  return 'indexedDB' in window;
};

// IndexedDB database configuration
const DB_NAME = 'kogidiDatabase';
const DB_VERSION = 1;
const STORES = {
  courses: 'courses',
  lessons: 'lessons',
  progress: 'progress',
  settings: 'settings'
};

// Initialize the database
const initializeDB = () => {
  return new Promise<IDBDatabase>((resolve, reject) => {
    if (!isIndexedDBAvailable()) {
      reject('IndexedDB is not available in this browser');
      return;
    }
    
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    
    request.onerror = (event) => {
      reject('Failed to open database');
    };
    
    request.onsuccess = (event) => {
      const db = request.result;
      resolve(db);
    };
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.courses)) {
        db.createObjectStore(STORES.courses, { keyPath: 'id' });
      }
      
      if (!db.objectStoreNames.contains(STORES.lessons)) {
        db.createObjectStore(STORES.lessons, { keyPath: 'id' });
      }
      
      if (!db.objectStoreNames.contains(STORES.progress)) {
        db.createObjectStore(STORES.progress, { keyPath: 'id' });
      }
      
      if (!db.objectStoreNames.contains(STORES.settings)) {
        db.createObjectStore(STORES.settings, { keyPath: 'id' });
      }
    };
  });
};

// Generic function to add or update data in a store
const saveData = async (storeName: string, data: any) => {
  try {
    const db = await initializeDB();
    
    return new Promise<void>((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.put(data);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(`Failed to save data to ${storeName}`);
      
      transaction.oncomplete = () => db.close();
    });
  } catch (error) {
    console.error('Error saving data:', error);
    throw error;
  }
};

// Generic function to retrieve data from a store by ID
const getData = async (storeName: string, id: string) => {
  try {
    const db = await initializeDB();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      
      const request = store.get(id);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(`Failed to get data from ${storeName}`);
      
      transaction.oncomplete = () => db.close();
    });
  } catch (error) {
    console.error('Error getting data:', error);
    throw error;
  }
};

// Generic function to get all data from a store
const getAllData = async (storeName: string) => {
  try {
    const db = await initializeDB();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      
      const request = store.getAll();
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(`Failed to get all data from ${storeName}`);
      
      transaction.oncomplete = () => db.close();
    });
  } catch (error) {
    console.error('Error getting all data:', error);
    throw error;
  }
};

// Generic function to delete data from a store by ID
const deleteData = async (storeName: string, id: string) => {
  try {
    const db = await initializeDB();
    
    return new Promise<void>((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.delete(id);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(`Failed to delete data from ${storeName}`);
      
      transaction.oncomplete = () => db.close();
    });
  } catch (error) {
    console.error('Error deleting data:', error);
    throw error;
  }
};

// Course-specific functions
export const courseStorage = {
  saveCourse: async (course: any) => {
    return saveData(STORES.courses, course);
  },
  
  getCourse: async (id: string) => {
    return getData(STORES.courses, id);
  },
  
  getAllCourses: async () => {
    return getAllData(STORES.courses);
  },
  
  deleteCourse: async (id: string) => {
    return deleteData(STORES.courses, id);
  }
};

// Lesson-specific functions
export const lessonStorage = {
  saveLesson: async (lesson: any) => {
    return saveData(STORES.lessons, lesson);
  },
  
  getLesson: async (id: string) => {
    return getData(STORES.lessons, id);
  },
  
  getLessonsForCourse: async (courseId: string) => {
    try {
      const allLessons = await getAllData(STORES.lessons) as any[];
      return allLessons.filter(lesson => lesson.courseId === courseId);
    } catch (error) {
      console.error('Error getting lessons for course:', error);
      throw error;
    }
  },
  
  deleteLesson: async (id: string) => {
    return deleteData(STORES.lessons, id);
  }
};

// Progress-specific functions
export const progressStorage = {
  saveProgress: async (progress: any) => {
    return saveData(STORES.progress, progress);
  },
  
  getProgress: async (id: string) => {
    return getData(STORES.progress, id);
  },
  
  getAllProgress: async () => {
    return getAllData(STORES.progress);
  }
};

// Settings-specific functions
export const settingsStorage = {
  saveSettings: async (settings: any) => {
    return saveData(STORES.settings, settings);
  },
  
  getSettings: async (id: string) => {
    return getData(STORES.settings, id);
  },
  
  getUserSettings: async () => {
    return getData(STORES.settings, 'userSettings');
  }
};

// Check storage usage and available space
export const checkStorageUsage = async () => {
  if (navigator.storage && navigator.storage.estimate) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        usage: estimate.usage || 0,
        quota: estimate.quota || 0,
        percentUsed: estimate.usage && estimate.quota 
          ? ((estimate.usage / estimate.quota) * 100).toFixed(2)
          : '0',
      };
    } catch (error) {
      console.error('Error estimating storage:', error);
      return { usage: 0, quota: 0, percentUsed: '0' };
    }
  } else {
    console.warn('Storage API not available');
    return { usage: 0, quota: 0, percentUsed: '0' };
  }
};

// Fallback to localStorage if IndexedDB is not available
export const localStorageFallback = {
  saveItem: (key: string, value: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  },
  
  getItem: (key: string) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error getting from localStorage:', error);
      return null;
    }
  },
  
  removeItem: (key: string) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }
};
