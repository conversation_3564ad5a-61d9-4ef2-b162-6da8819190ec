
// API Service for KoGidi

// Base URLs for different environments
const API_URLS = {
  development: 'http://localhost:8000/api/v1',
  production: 'https://kogidi-be.onrender.com/api/v1',
  test: 'http://localhost:8000/api'
};

// Get current environment
const ENV = process.env.NODE_ENV || 'development';
const BASE_URL = API_URLS[ENV as keyof typeof API_URLS];

// Helper function for making API requests
const fetchAPI = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  
  // Add default headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Get auth token if available
  const token = localStorage.getItem('kogidi-token');
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle non-200 responses
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Something went wrong');
    }

    // Return JSON data
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Authentication services
export const authService = {
  login: async (email: string, password: string) => {
    // For now, this uses mock data in AppContext.tsx
    // In a real app, this would call the actual API
    return fetchAPI('/auth/login/', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  },
  
  register: async (name: string, email: string, password: string, isTeacher: boolean) => {
    return fetchAPI('/auth/signup/', {
      method: 'POST',
      body: JSON.stringify({ name, email, password, isTeacher }),
    });
  },
  
  forgotPassword: async (email: string) => {
    return fetchAPI('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },
};

// Course services
export const courseService = {
  getAllCourses: async () => {
    // For now, using mock data in AppContext.tsx
    return fetchAPI('/courses');
  },
  
  getCourseById: async (id: string) => {
    return fetchAPI(`/courses/${id}`);
  },
  
  searchCourses: async (query: string, filters: Record<string, any>) => {
    return fetchAPI(`/courses/search?q=${query}`, {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  },
};

// Lesson services
export const lessonService = {
  getLessonsForCourse: async (courseId: string) => {
    return fetchAPI(`/courses/${courseId}/lessons`);
  },
  
  getLessonById: async (courseId: string, lessonId: string) => {
    return fetchAPI(`/courses/${courseId}/lessons/${lessonId}`);
  }
};

// Progress tracking services
export const progressService = {
  getUserProgress: async () => {
    return fetchAPI('/user/progress');
  },
  
  updateProgress: async (courseId: string, lessonId: string, progress: number) => {
    return fetchAPI('/user/progress', {
      method: 'POST',
      body: JSON.stringify({ courseId, lessonId, progress }),
    });
  },
  
  getMilestones: async () => {
    return fetchAPI('/user/milestones');
  }
};
