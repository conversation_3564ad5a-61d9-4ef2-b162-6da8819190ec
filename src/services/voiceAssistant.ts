
// Voice Assistant Service
// Uses Web Speech API for speech recognition and synthesis

// TypeScript declarations for Web Speech API
interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  onstart: (event: Event) => void;
  onend: (event: Event) => void;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionErrorEvent) => void;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
}

interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  isFinal: boolean;
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionConstructor {
  new (): SpeechRecognition;
}

// Augment the Window interface to include Web Speech API
declare global {
  interface Window {
    SpeechRecognition?: SpeechRecognitionConstructor;
    webkitSpeechRecognition?: SpeechRecognitionConstructor;
  }
}

interface VoiceAssistantOptions {
  language?: string;
  onResult?: (text: string) => void;
  onInterim?: (text: string) => void;
  onError?: (error: string) => void;
  onStart?: () => void;
  onEnd?: () => void;
}

interface SpeakOptions {
  rate?: number;
  pitch?: number;
  voice?: SpeechSynthesisVoice;
  language?: string;
  onEnd?: () => void;
}

// Knowledge base for educational content
const knowledgeBase = {
  photosynthesis: {
    en: "Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water. It produces oxygen as a byproduct and is crucial for maintaining Earth's oxygen levels.",
    yo: "Ìṣẹ̀dá fótósíntẹ́sìsì jẹ́ ìlànà tí àwọn igi alawọ́ ewé àti àwọn ẹ̀dá míràn fi lo ìmọ́lẹ̀ oòrùn láti ṣẹ̀dá oúnjẹ pẹ̀lú káàbọ̀ńdáọ́ksáìdì àti omi. Ó ń ṣẹ̀dá ọ́síjìn gẹ́gẹ́ bí èrè àjálù, ó sì ṣe pàtàkì fún ìdúró ìpòsí ọ́síjìn ayé.",
    ig: "Photosynthesis bụ usoro nke osisi akwụkwọ ndụ na ụfọdụ ihe ndị ọzọ si ejiri anyanwụ emepụta nri site na carbon dioxide na mmiri. Ọ na-emepụta oxygen dị ka ngwakọta na ọ dị mkpa maka idebe ọkwa oxygen nke Ụwa.",
    ha: "Photosynthesis ita ne aikin da tsirrai masu kore da wasu halittu ke amfani da hasken rana domin sarrafa abinci tare da carbon dioxide da ruwa. Yana samar da oxygen a matsayin sakamakon kuma yana da mahimmanci don kiyaye matakan oxygen na duniya."
  },
  mathematics: {
    en: "Mathematics is the study of numbers, shapes, and patterns. It's used in everyday activities from calculating costs to measuring materials, and serves as the foundation for sciences, engineering, and technology.",
    yo: "Ìṣirò jẹ́ ẹ̀kọ́ nípa àwọn nọ́mbà, àwọn àwòrán, àti àwọn ẹ̀yà. A ń lò ó nínú àwọn ìṣeṣe ojoojúmọ́ láti ṣírò owó títí dé ìwọ̀n àwọn ohun èlò, ó sì jẹ́ ìpìlẹ̀ fún àwọn sáyẹ́nsì, ìṣẹ́ ẹ̀rọ, àti ìgbélárugẹ.",
    ig: "Mathematics bụ ịmụ banyere ọnụọgụ, ọdịdị, na usoro. Ana-eji ya eme omume kwa ụbọchị site na ịgụta ọnụ ego ruo n'inyocha ihe, ma ọ na-ejikwa ngwakọ sayensị, ọrụ injinia, yana teknọlọjị.",
    ha: "Mathematics shine nazarin lambobi, siffofi, da tsari. Ana amfani dashi a ayyukan yau da kullum daga kididdiga farashi zuwa awon abubuwa, kuma yana matsayi a ginshikin kimiyya, injiniya, da fasaha."
  },
  greetings: {
    en: "Hello! How can I help you with your learning today?",
    yo: "Báwo! Báwo ni mo ṣe lè ràn ọ́ lọ́wọ́ pẹ̀lú ẹ̀kọ́ rẹ lónìí?",
    ig: "Ndewo! Kedu ka m ga-esi nyere gị aka na mmụta gị taa?",
    ha: "Sannu! Ta yaya zan taimaka maka koyon ka a yau?"
  },
  translation: {
    en: {
      yo: "Hello in Yoruba is 'Báwo'",
      ig: "Hello in Igbo is 'Ndewo'",
      ha: "Hello in Hausa is 'Sannu'"
    },
    yo: {
      en: "Báwo in English is 'Hello'",
      ig: "Báwo in Igbo is 'Ndewo'",
      ha: "Báwo in Hausa is 'Sannu'"
    },
    ig: {
      en: "Ndewo in English is 'Hello'",
      yo: "Ndewo in Yoruba is 'Báwo'",
      ha: "Ndewo in Hausa is 'Sannu'"
    },
    ha: {
      en: "Sannu in English is 'Hello'",
      yo: "Sannu in Yoruba is 'Báwo'",
      ig: "Sannu in Igbo is 'Ndewo'"
    }
  }
};

class VoiceAssistant {
  private recognition: SpeechRecognition | null = null;
  synthesis: SpeechSynthesis | null = null;
  private isListening: boolean = false;
  private language: string;
  onResult: (text: string) => void = () => {};
  onInterim: (text: string) => void = () => {};
  onError: (error: string) => void = () => {};
  onStart: () => void = () => {};
  onEnd: () => void = () => {};
  
  constructor(options: VoiceAssistantOptions = {}) {
    this.language = options.language || 'en-NG';
    this.onResult = options.onResult || (() => {});
    this.onInterim = options.onInterim || (() => {});
    this.onError = options.onError || ((error) => console.error(error));
    this.onStart = options.onStart || (() => {});
    this.onEnd = options.onEnd || (() => {});
    
    this.initSpeechRecognition();
    this.initSpeechSynthesis();
  }
  
  private initSpeechRecognition() {
    // Check if Speech Recognition API is available
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      console.error('Speech Recognition API is not supported in this browser');
      return;
    }
    
    this.recognition = new SpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = this.language;
    
    this.recognition.onstart = () => {
      this.isListening = true;
      this.onStart();
    };
    
    this.recognition.onend = () => {
      this.isListening = false;
      this.onEnd();
    };
    
    this.recognition.onresult = (event) => {
      let interimTranscript = '';
      let finalTranscript = '';
      
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
      
      if (interimTranscript) {
        this.onInterim(interimTranscript);
      }
      
      if (finalTranscript) {
        this.onResult(finalTranscript);
      }
    };
    
    this.recognition.onerror = (event) => {
      this.onError(`Speech recognition error: ${event.error}`);
    };
  }
  
  private initSpeechSynthesis() {
    // Check if Speech Synthesis API is available
    if ('speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis;
    } else {
      console.error('Speech Synthesis API is not supported in this browser');
    }
  }
  
  public setLanguage(language: string) {
    this.language = language;
    if (this.recognition) {
      this.recognition.lang = language;
    }
  }
  
  public startListening() {
    if (!this.recognition) {
      this.onError('Speech Recognition is not supported');
      return;
    }
    
    if (!this.isListening) {
      try {
        this.recognition.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    }
  }
  
  public stopListening() {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
    }
  }
  
  public speak(text: string, options: SpeakOptions = {}) {
    if (!this.synthesis) {
      this.onError('Speech Synthesis is not supported');
      return;
    }
    
    // Stop any current speech
    this.synthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Set language based on current language or option
    utterance.lang = options.language || this.language;
    
    // Set options
    if (options.rate !== undefined) utterance.rate = options.rate;
    if (options.pitch !== undefined) utterance.pitch = options.pitch;
    if (options.voice) utterance.voice = options.voice;
    
    // Add event handler for speech end
    if (options.onEnd) {
      utterance.onend = options.onEnd;
    }
    
    this.synthesis.speak(utterance);
  }
  
  public getVoices(): Promise<SpeechSynthesisVoice[]> {
    return new Promise((resolve) => {
      if (!this.synthesis) {
        resolve([]);
        return;
      }
      
      // If voices are already loaded
      let voices = this.synthesis.getVoices();
      if (voices.length > 0) {
        resolve(voices);
        return;
      }
      
      // If voices are not loaded yet, wait for them to be loaded
      this.synthesis.onvoiceschanged = () => {
        voices = this.synthesis.getVoices();
        resolve(voices);
      };
    });
  }
  
  public isSupported() {
    return Boolean(this.recognition && this.synthesis);
  }
  
  public isActive() {
    return this.isListening;
  }
  
  // Process user input and generate response
  public async processInput(text: string): Promise<string> {
    const lowerText = text.toLowerCase().trim();
    const currentLanguage = this.language.split('-')[0] || 'en';
    
    // Check for specific educational topics in the knowledgeBase
    if (lowerText.includes('photosynthesis')) {
      return knowledgeBase.photosynthesis[currentLanguage] || knowledgeBase.photosynthesis.en;
    }
    
    if (lowerText.includes('mathematics') || lowerText.includes('math') || lowerText.includes('equation')) {
      return knowledgeBase.mathematics[currentLanguage] || knowledgeBase.mathematics.en;
    }
    
    // Check for greetings
    if (lowerText.includes('hello') || lowerText.includes('hi')) {
      return knowledgeBase.greetings[currentLanguage] || knowledgeBase.greetings.en;
    }
    
    // Check for translation requests
    if (lowerText.includes('translate') || lowerText.includes('translation')) {
      const languages = {
        english: 'en',
        yoruba: 'yo',
        igbo: 'ig',
        hausa: 'ha'
      };
      
      // Try to determine source and target languages from the text
      let sourceLang = currentLanguage;
      let targetLang = 'en'; // Default to English if not specified
      
      for (const [langName, langCode] of Object.entries(languages)) {
        if (lowerText.includes(`to ${langName.toLowerCase()}`)) {
          targetLang = langCode;
        }
      }
      
      // If the text contains 'hello', use our predefined translations
      if (lowerText.includes('hello')) {
        return knowledgeBase.translation[sourceLang]?.[targetLang] || 
               `Sorry, I don't have a translation for that combination of languages.`;
      }
    }
    
    // Default response if no matching topic is found
    const defaultResponses = {
      en: "I'm sorry, I don't have an answer for that question yet. But I'm always learning!",
      yo: "Mo kábàámọ̀, Emi kò ní ìdáhùn sí ìbéèrè yẹn síbẹ̀síbẹ̀. Ṣùgbọ́n mo ń kọ́ ẹ̀kọ́ nígbà gbogbo!",
      ig: "Ndo, enweghị m azịza maka ajụjụ ahụ ugbu a. Ma na-amụ mgbe niile!",
      ha: "Yi haƙuri, ban da amsar wannan tambayar nan har yanzu ba. Amma koyaushe ina koya!"
    };
    
    return defaultResponses[currentLanguage] || defaultResponses.en;
  }
}

// Export an instance for use throughout the app
export const voiceAssistant = new VoiceAssistant();

// Also export the class for creating custom instances
export { VoiceAssistant };
export type { VoiceAssistantOptions, SpeakOptions };
