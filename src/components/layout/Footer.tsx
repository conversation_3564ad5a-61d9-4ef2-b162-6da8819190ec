import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";
import { Github, Facebook, Instagram, Linkedin, Twitter } from "lucide-react";
import logoSvg from "@/assets/logo.svg";

const Footer = () => {
  const { t } = useTranslation();
  const isMobile = useIsMobile();

  const footerAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemAnimation = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.footer
      className="bg-kogidi-dark dark:bg-kogidi-dark/90 text-white"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
      variants={footerAnimation}
    >
      <div className="container mx-auto py-12 px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <motion.div className="space-y-4" variants={itemAnimation}>
            <Link to="/" className="flex items-center gap-2 group">
              <motion.img
                src={logoSvg}
                alt="KoGidi Logo"
                className="h-10 w-10"
                whileHover={{ scale: 1.1 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              />
              <span className="text-xl font-bold group-hover:text-kogidi-teal transition-colors">
                {t("app.name")}
              </span>
            </Link>
            <p className="text-white/80 text-sm">{t("app.tagline")}</p>
          </motion.div>

          <motion.div variants={itemAnimation}>
            <h3 className="font-semibold text-lg mb-4 text-white">
              {t("nav.links")}
            </h3>
            <ul className="space-y-2">
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Link
                  to="/"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm"
                >
                  {t("nav.home")}
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Link
                  to="/courses"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm"
                >
                  {t("nav.courses")}
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Link
                  to="/about"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm"
                >
                  {t("nav.about")}
                </Link>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Link
                  to="/contact"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm"
                >
                  {t("nav.contact")}
                </Link>
              </motion.li>
            </ul>
          </motion.div>

          <motion.div variants={itemAnimation}>
            <h3 className="font-semibold text-lg mb-4 text-white">
              {t("nav.download")}
            </h3>
            <ul className="space-y-2">
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <a
                  href="#"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm flex items-center gap-2"
                >
                  <span className="bg-white/10 p-1 rounded">iOS</span>
                  App Store
                </a>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <a
                  href="#"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm flex items-center gap-2"
                >
                  <span className="bg-white/10 p-1 rounded">Android</span>
                  Google Play
                </a>
              </motion.li>
              <motion.li
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <a
                  href="#"
                  className="text-white/80 hover:text-kogidi-teal transition-colors text-sm flex items-center gap-2"
                >
                  <span className="bg-white/10 p-1 rounded">Win/Mac</span>
                  Desktop
                </a>
              </motion.li>
            </ul>
          </motion.div>

          <motion.div variants={itemAnimation}>
            <h3 className="font-semibold text-lg mb-4 text-white">
              {t("nav.social")}
            </h3>
            <div className="flex flex-wrap gap-3">
              <motion.a
                href="#"
                className="bg-white/10 hover:bg-kogidi-teal/50 transition-colors text-white rounded-full p-2"
                whileHover={{ y: -4, scale: 1.15 }}
                whileTap={{ scale: 0.9 }}
              >
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </motion.a>
              <motion.a
                href="#"
                className="bg-white/10 hover:bg-kogidi-teal/50 transition-colors text-white rounded-full p-2"
                whileHover={{ y: -4, scale: 1.15 }}
                whileTap={{ scale: 0.9 }}
              >
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </motion.a>
              <motion.a
                href="#"
                className="bg-white/10 hover:bg-kogidi-teal/50 transition-colors text-white rounded-full p-2"
                whileHover={{ y: -4, scale: 1.15 }}
                whileTap={{ scale: 0.9 }}
              >
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </motion.a>
              <motion.a
                href="#"
                className="bg-white/10 hover:bg-kogidi-teal/50 transition-colors text-white rounded-full p-2"
                whileHover={{ y: -4, scale: 1.15 }}
                whileTap={{ scale: 0.9 }}
              >
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </motion.a>
              <motion.a
                href="#"
                className="bg-white/10 hover:bg-kogidi-teal/50 transition-colors text-white rounded-full p-2"
                whileHover={{ y: -4, scale: 1.15 }}
                whileTap={{ scale: 0.9 }}
              >
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </motion.a>
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium text-white mb-2">
                {t("nav.newsletter")}
              </h4>
              <div className="flex max-w-md">
                <input
                  type="email"
                  placeholder={t("nav.emailPlaceholder")}
                  className="bg-white/10 text-white rounded-l px-3 py-2 w-full text-sm focus:outline-none focus:ring-1 focus:ring-kogidi-teal"
                />
                <button className="bg-kogidi-gold hover:bg-kogidi-gold/90 text-kogidi-dark px-4 py-2 rounded-r text-sm font-medium transition-colors">
                  {t("nav.subscribe")}
                </button>
              </div>
            </div>
          </motion.div>
        </div>

        <motion.div
          variants={itemAnimation}
          className="border-t border-white/10 mt-10 pt-6 text-center text-white/60 text-sm"
        >
          <p>{t("landing.footer.copyright")}</p>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;
