
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useAppContext } from '@/contexts/AppContext';
import { useAuth } from '@/hooks/useAuth';
import LanguageSelector from '../shared/LanguageSelector';
import AuthModal from '../auth/AuthModal';
import { Mic, Menu } from 'lucide-react';
import { motion } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';

// Direct import from assets folder
import logoSvg from '@/assets/logo.svg';

const Header = () => {
  const { t } = useTranslation();
  const { activateVoiceAssistant } = useAppContext();
  const { isAuthenticated, user, logout } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'login' | 'signup'>('login');
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  
  const handleLoginClick = () => {
    navigate('/login');
  };
  
  const handleSignupClick = () => {
    navigate('/signup');
  };
  
  const handleVoiceClick = () => {
    activateVoiceAssistant();
  };
  
  const handleLogoutClick = async () => {
    await logout();
  };
  
  const navAnimation = {
    hidden: { y: -20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
        delayChildren: 0.1,
      }
    }
  };
  
  const navItemAnimation = {
    hidden: { y: -10, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };
  
  return (
    <motion.header 
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="border-b bg-white sticky top-0 z-40 w-full"
    >
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-6">
          <Link to="/" className="flex items-center gap-2">
            <motion.div 
              className="flex items-center gap-1"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <motion.img 
                src={logoSvg} 
                alt="KoGidi Logo" 
                className="h-8 w-auto" 
                whileHover={{ rotate: [0, -5, 5, -3, 0] }}
                transition={{ duration: 0.5 }}
              />
              <span className="text-xl font-bold hidden sm:inline-block text-kogidi-dark">{t('app.name')}</span>
            </motion.div>
          </Link>
          
          <motion.nav 
            className="hidden md:flex gap-6"
            initial="hidden"
            animate="visible"
            variants={navAnimation}
          >
            <motion.div variants={navItemAnimation} whileHover={{ scale: 1.05 }}>
              <Link to="/" className="text-sm font-medium hover:text-kogidi-green transition-colors">
                {t('nav.home')}
              </Link>
            </motion.div>
            <motion.div variants={navItemAnimation} whileHover={{ scale: 1.05 }}>
              <Link to="/courses" className="text-sm font-medium hover:text-kogidi-green transition-colors">
                {t('nav.courses')}
              </Link>
            </motion.div>
            {isAuthenticated && (
              <>
                <motion.div variants={navItemAnimation} whileHover={{ scale: 1.05 }}>
                  <Link to="/dashboard" className="text-sm font-medium hover:text-kogidi-green transition-colors">
                    {t('nav.dashboard')}
                  </Link>
                </motion.div>
                <motion.div variants={navItemAnimation} whileHover={{ scale: 1.05 }}>
                  <Link to="/progress" className="text-sm font-medium hover:text-kogidi-green transition-colors">
                    {t('nav.progress')}
                  </Link>
                </motion.div>
              </>
            )}
          </motion.nav>
        </div>
        
        <div className="flex items-center gap-4">
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button 
              variant="ghost" 
              size="icon"
              className="rounded-full bg-kogidi-green text-white hover:bg-kogidi-green/90"
              onClick={handleVoiceClick}
            >
              <Mic className="h-4 w-4" />
              <span className="sr-only">Voice Assistant</span>
            </Button>
          </motion.div>
          
          <LanguageSelector />
          
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div whileHover={{ scale: 1.05 }}>
                  <Button variant="ghost" className="rounded-full h-8 w-8 p-0">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="https://github.com/shadcn.png" alt={user?.name} />
                      <AvatarFallback className="bg-kogidi-teal text-white">
                        {user?.name?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link to="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/profile">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogoutClick}>
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="hidden sm:flex items-center gap-2">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button variant="ghost" onClick={handleLoginClick} className="text-kogidi-dark hover:text-kogidi-green">
                  {t('auth.login')}
                </Button>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button variant="brand" onClick={handleSignupClick}>
                  {t('auth.signup')}
                </Button>
              </motion.div>
            </div>
          )}
          
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Open menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px]">
              <div className="grid gap-6 py-6">
                <Link to="/" className="flex items-center gap-2">
                  <img src={logoSvg} alt="KoGidi Logo" className="h-6 w-auto" />
                  <span className="text-xl font-bold text-kogidi-dark">{t('app.name')}</span>
                </Link>
                <motion.div 
                  className="grid gap-3"
                  initial="hidden"
                  animate="visible"
                  variants={navAnimation}
                >
                  <motion.div variants={navItemAnimation}>
                    <Link to="/" className="text-lg font-medium">
                      {t('nav.home')}
                    </Link>
                  </motion.div>
                  <motion.div variants={navItemAnimation}>
                    <Link to="/courses" className="text-lg font-medium">
                      {t('nav.courses')}
                    </Link>
                  </motion.div>
                  {isAuthenticated && (
                    <>
                      <motion.div variants={navItemAnimation}>
                        <Link to="/dashboard" className="text-lg font-medium">
                          {t('nav.dashboard')}
                        </Link>
                      </motion.div>
                      <motion.div variants={navItemAnimation}>
                        <Link to="/progress" className="text-lg font-medium">
                          {t('nav.progress')}
                        </Link>
                      </motion.div>
                      <motion.div variants={navItemAnimation}>
                        <Link to="/settings" className="text-lg font-medium">
                          {t('nav.settings')}
                        </Link>
                      </motion.div>
                      <motion.div variants={navItemAnimation}>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start" 
                          onClick={handleLogoutClick}
                        >
                          {t('auth.logout')}
                        </Button>
                      </motion.div>
                    </>
                  )}
                </motion.div>
                
                {!isAuthenticated && (
                  <div className="grid gap-2">
                    <Button variant="outline" onClick={handleLoginClick} className="w-full">
                      {t('auth.login')}
                    </Button>
                    <Button variant="brand" onClick={handleSignupClick} className="w-full">
                      {t('auth.signup')}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
