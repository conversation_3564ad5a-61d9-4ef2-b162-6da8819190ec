
import { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Header from './Header';
import Footer from './Footer';
import VoiceAssistant from '../shared/VoiceAssistant';
import { useAppContext } from '@/contexts/AppContext';

interface LayoutProps {
  children: ReactNode;
  hideFooter?: boolean;
}

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1],
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.3,
    },
  },
};

const Layout = ({ children, hideFooter = false }: LayoutProps) => {
  const { t } = useTranslation();
  const { isVoiceAssistantActive, deactivateVoiceAssistant } = useAppContext();
  
  return (
    <div className="flex flex-col min-h-screen overflow-hidden bg-white">
      <Header />
      <motion.main 
        className="flex-1 w-full max-w-[1600px] mx-auto px-4 sm:px-6"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants}
        key="main-content"
      >
        {children}
      </motion.main>
      {!hideFooter && <Footer />}
      
      <AnimatePresence mode="wait">
        {isVoiceAssistantActive && (
          <motion.div
            key="voice-assistant"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <VoiceAssistant 
              isOpen={isVoiceAssistantActive} 
              onClose={deactivateVoiceAssistant} 
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Layout;
