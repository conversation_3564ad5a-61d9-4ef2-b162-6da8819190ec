import { ReactNode } from "react";
import { motion } from "framer-motion";

interface UserStoryProps {
  name: string;
  role: string;
  image: ReactNode;
  quote: string;
  benefit: string;
  avatar?: string;
  location?: string;
}

const UserStory = ({
  name,
  role,
  image,
  quote,
  benefit,
  avatar,
  location,
}: UserStoryProps) => {
  return (
    <motion.div
      className="kogidi-card p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
      whileHover={{ y: -5, boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)" }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex flex-col items-center text-center mb-4"
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <motion.div
          className="mb-4 w-24 h-24"
          whileHover={{ scale: 1.05, rotate: [0, -3, 3, 0] }}
          transition={{ duration: 0.3 }}
        >
          {avatar ? (
            <img
              src={avatar}
              alt={name}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            image
          )}
        </motion.div>
        <motion.div>
          <h4 className="font-semibold text-lg text-foreground">{name}</h4>
          <p className="text-muted-foreground text-sm">{role}</p>
          {location && (
            <p className="text-muted-foreground/70 text-xs">{location}</p>
          )}
        </motion.div>
      </motion.div>

      <motion.blockquote
        className="relative text-muted-foreground italic mb-4 px-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        <span className="absolute -top-2 -left-1 text-4xl text-kogidi-gold font-serif">
          "
        </span>
        <p className="relative z-10">{quote}</p>
        <span className="absolute -bottom-4 -right-1 text-4xl text-kogidi-gold font-serif">
          "
        </span>
      </motion.blockquote>

      <motion.div
        className="bg-kogidi-green/10 rounded-md p-3 text-sm"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        whileHover={{ scale: 1.02 }}
      >
        <p className="text-kogidi-green font-medium">✓ {benefit}</p>
      </motion.div>
    </motion.div>
  );
};

export default UserStory;
