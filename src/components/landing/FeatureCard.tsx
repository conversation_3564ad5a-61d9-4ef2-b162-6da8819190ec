
import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface FeatureCardProps {
  icon: string | ReactNode;
  title: string;
  description: string;
  className?: string;
}

const FeatureCard = ({ icon, title, description, className = '' }: FeatureCardProps) => {
  return (
    <motion.div 
      className={`p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 bg-white ${className}`}
      whileHover={{ y: -10, boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)" }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <motion.div 
        className="mb-4 bg-kogidi-green/10 rounded-full w-16 h-16 flex items-center justify-center overflow-hidden"
        whileHover={{ rotate: [0, 10, -10, 0], scale: 1.05 }}
        transition={{ duration: 0.5 }}
      >
        {typeof icon === 'string' ? (
          <img src={icon} alt={title} className="w-10 h-10 object-contain" />
        ) : (
          icon
        )}
      </motion.div>
      <motion.h3 
        className="text-xl font-semibold mb-3 text-kogidi-dark"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {title}
      </motion.h3>
      <motion.p 
        className="text-kogidi-dark/70"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {description}
      </motion.p>
    </motion.div>
  );
};

export default FeatureCard;
