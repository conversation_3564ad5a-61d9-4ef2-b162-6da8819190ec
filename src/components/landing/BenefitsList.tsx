
import { useTranslation } from 'react-i18next';
import { Check } from 'lucide-react';

const BenefitsList = () => {
  const { t } = useTranslation();
  
  const benefits = [
    'landing.benefits.simplified',
    'landing.benefits.offline',
    'landing.benefits.voice',
    'landing.benefits.progress',
    'landing.benefits.localized',
    'landing.benefits.affordable'
  ];
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {benefits.map((benefit, index) => (
        <div key={index} className="flex items-start">
          <div className="mt-1 mr-3 bg-kogidi-green/20 rounded-full p-1">
            <Check className="h-4 w-4 text-kogidi-green" />
          </div>
          <p className="text-kogidi-dark/80">{t(benefit)}</p>
        </div>
      ))}
    </div>
  );
};

export default BenefitsList;
