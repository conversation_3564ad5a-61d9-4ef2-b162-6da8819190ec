
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from "@/components/ui/button";
import { Mic, X } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from '@/components/ui/use-toast';

interface VoiceAssistantProps {
  isOpen: boolean;
  onClose: () => void;
}

// Example questions by language
const exampleQuestions: Record<string, string[]> = {
  en: [
    "What is photosynthesis?",
    "How do I solve this equation?",
    "Translate 'Hello' to Yoruba"
  ],
  yo: [
    "Kini photosynthesis?",
    "Bawo ni mo ṣe le yanju ìṣiro yii?",
    "Tumọ 'Pẹlẹ' si Gẹẹsi"
  ],
  ig: [
    "Gịnị bụ photosynthesis?",
    "Kedu ka m ga-esi dozie mgbakọ a?",
    "Tụgharịa 'Nnọọ' na Bekee"
  ],
  ha: [
    "Mene ne photosynthesis?",
    "Yaya zan warware wannan lissafi?",
    "Fassara 'Sannu' zuwa Turanci"
  ]
};

const VoiceAssistant = ({ isOpen, onClose }: VoiceAssistantProps) => {
  const { t, i18n } = useTranslation();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  
  const currentLanguage = i18n.language || 'en';
  const questions = exampleQuestions[currentLanguage] || exampleQuestions.en;
  
  useEffect(() => {
    if (!isOpen) {
      setIsListening(false);
      setTranscript('');
      setResponse('');
    }
  }, [isOpen]);
  
  const handleListenClick = () => {
    if (!isListening) {
      startListening();
    } else {
      stopListening();
    }
  };
  
  const startListening = () => {
    setIsListening(true);
    setTranscript('');
    setResponse('');
    
    // Simulate recording with Web Speech API
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.lang = currentLanguage;
      recognition.continuous = false;
      recognition.interimResults = false;
      
      recognition.onresult = (event) => {
        const speechResult = event.results[0][0].transcript;
        setTranscript(speechResult);
        processQuery(speechResult);
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error', event.error);
        setIsListening(false);
        toast({
          title: t('voice.error', 'Speech Recognition Error'),
          description: t('voice.errorDescription', 'There was a problem with speech recognition. Please try again.'),
          variant: "destructive",
        });
      };
      
      recognition.onend = () => {
        setIsListening(false);
      };
      
      recognition.start();
    } else {
      // Fallback for browsers without Web Speech API
      setTimeout(() => {
        setIsListening(false);
        toast({
          title: t('voice.notSupported', 'Feature Not Supported'),
          description: t('voice.notSupportedDescription', 'Your browser does not support speech recognition.'),
          variant: "destructive",
        });
      }, 1000);
    }
  };
  
  const stopListening = () => {
    setIsListening(false);
    
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      recognition.stop();
    }
  };
  
  const processQuery = (query: string) => {
    // Simulate AI processing
    setResponse(t('voice.thinking', 'Thinking...'));
    
    // In a real implementation, this would call an API
    setTimeout(() => {
      // Sample responses (would be replaced with actual AI responses)
      const responses: Record<string, string> = {
        photosynthesis: t('voice.responses.photosynthesis', 'Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize nutrients from carbon dioxide and water.'),
        equation: t('voice.responses.equation', 'To solve an equation, you need to isolate the variable. For example, in x + 5 = 10, subtract 5 from both sides to get x = 5.'),
        translate: t('voice.responses.translate', 'Hello in Yoruba is "Pẹlẹ". In Igbo it is "Nnọọ". In Hausa it is "Sannu".'),
        default: t('voice.responses.default', 'I\'m sorry, I don\'t have an answer for that question. Could you try asking something else?')
      };
      
      const queryLower = query.toLowerCase();
      let responseText = responses.default;
      
      if (queryLower.includes('photosynthesis')) {
        responseText = responses.photosynthesis;
      } else if (queryLower.includes('equation') || queryLower.includes('solve')) {
        responseText = responses.equation;
      } else if (queryLower.includes('translate')) {
        responseText = responses.translate;
      }
      
      setResponse(responseText);
      
      // Text to speech
      if ('speechSynthesis' in window) {
        const speech = new SpeechSynthesisUtterance(responseText);
        speech.lang = currentLanguage;
        window.speechSynthesis.speak(speech);
      }
    }, 1500);
  };
  
  const handleQuestionClick = (question: string) => {
    setTranscript(question);
    processQuery(question);
  };
  
  return (
    <div className="bg-white rounded-lg shadow-lg p-4 w-full sm:w-80 max-w-full">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-lg flex items-center">
          <Mic className="mr-2 h-5 w-5 text-kogidi-green" />
          {t('voice.assistant', 'Voice Assistant')}
        </h3>
        <Button 
          variant="ghost" 
          size="sm" 
          className="rounded-full h-8 w-8 p-0" 
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>
      
      <div className="mb-4">
        <h4 className="font-medium mb-2">{currentLanguage}</h4>
        <p className="text-sm text-muted-foreground mb-2">
          {t('voice.tryAsking', 'Try asking:')}
        </p>
        
        <div className="space-y-2">
          {questions.map((question, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start text-left text-sm"
                onClick={() => handleQuestionClick(question)}
              >
                "{question}"
              </Button>
            </motion.div>
          ))}
        </div>
      </div>
      
      {transcript && (
        <div className="mb-4 p-3 bg-muted rounded-md">
          <p className="text-sm font-medium">{t('voice.query', 'Your question:')}</p>
          <p className="text-sm">{transcript}</p>
        </div>
      )}
      
      {response && (
        <motion.div
          className="mb-4 p-3 bg-kogidi-green/10 text-kogidi-dark rounded-md"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-sm font-medium">KoGidi:</p>
          <p className="text-sm">{response}</p>
        </motion.div>
      )}
      
      <Button
        variant={isListening ? "destructive" : "brand"}
        className="w-full"
        onClick={handleListenClick}
      >
        {isListening ? t('voice.listening', 'Listening...') : t('voice.tapToSpeak', 'Tap to speak')}
      </Button>
    </div>
  );
};

export default VoiceAssistant;
