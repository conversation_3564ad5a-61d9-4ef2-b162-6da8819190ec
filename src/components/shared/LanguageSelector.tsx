
import { useTranslation } from 'react-i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppContext } from '@/contexts/AppContext';

const languages = [
  { code: 'en', name: 'English' },
  { code: 'yo', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { code: 'ig', name: '<PERSON><PERSON><PERSON>' },
  { code: 'ha', name: '<PERSON>usa' }
];

const LanguageSelector = () => {
  const { i18n } = useTranslation();
  const { changeLanguage } = useAppContext();
  
  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
    changeLanguage(value);
  };
  
  return (
    <Select defaultValue={i18n.language} onValueChange={handleLanguageChange}>
      <SelectTrigger className="w-32">
        <SelectValue placeholder="Language" />
      </SelectTrigger>
      <SelectContent>
        {languages.map((lang) => (
          <SelectItem key={lang.code} value={lang.code}>
            {lang.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default LanguageSelector;
