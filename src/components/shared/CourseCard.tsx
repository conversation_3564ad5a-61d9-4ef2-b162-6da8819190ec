
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Header } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAppContext } from "@/contexts/AppContext";
import { Download } from "lucide-react";

interface CourseCardProps {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  language: string;
  grade: string;
  subject: string;
  isDownloaded: boolean;
  progress: number;
}

const CourseCard = ({
  id,
  title,
  description,
  thumbnail,
  language,
  grade,
  subject,
  isDownloaded,
  progress
}: CourseCardProps) => {
  const { downloadCourse } = useAppContext();
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDownloading(true);
    
    // Simulate download
    setTimeout(() => {
      downloadCourse(id);
      setIsDownloading(false);
    }, 1500);
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-40 w-full overflow-hidden">
          <img 
            src={thumbnail} 
            alt={title} 
            className="w-full h-full object-cover transition-transform hover:scale-105 duration-300" 
          />
          <div className="absolute top-2 right-2 bg-white px-2 py-1 rounded text-xs font-medium">
            {subject}
          </div>
          <div className="absolute top-2 left-2 bg-primary text-white px-2 py-1 rounded text-xs font-medium">
            {grade}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-lg line-clamp-1">{title}</h3>
          {isDownloaded && (
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
              Downloaded
            </span>
          )}
        </div>
        <p className="text-muted-foreground text-sm line-clamp-2 mb-2">{description}</p>
        {progress > 0 && (
          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-1" />
          </div>
        )}
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between">
        <Link 
          to={isDownloaded ? `/courses/${id}` : '#'} 
          className={`text-sm ${!isDownloaded ? 'pointer-events-none text-muted-foreground' : 'text-primary hover:underline'}`}
        >
          {isDownloaded ? 'Open Course' : 'Available for Download'}
        </Link>
        {!isDownloaded && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleDownload}
            disabled={isDownloading}
          >
            <Download size={16} className={`mr-1 ${isDownloading ? 'animate-pulse-light' : ''}`} />
            {isDownloading ? 'Downloading...' : 'Download'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default CourseCard;
