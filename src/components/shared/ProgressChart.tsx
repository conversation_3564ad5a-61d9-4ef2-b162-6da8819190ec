
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from 'react-i18next';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Mock data for demonstration
const subjectData = [
  { name: 'Mathematics', score: 85, color: '#0288D1' },
  { name: 'English', score: 73, color: '#FBC02D' },
  { name: 'Science', score: 90, color: '#2E7D32' },
  { name: 'Social Studies', score: 68, color: '#9C27B0' },
];

const timeData = [
  { date: 'Week 1', hours: 8 },
  { date: 'Week 2', hours: 10 },
  { date: 'Week 3', hours: 7 },
  { date: 'Week 4', hours: 12 },
  { date: 'Week 5', hours: 9 },
  { date: 'Week 6', hours: 11 },
];

const pieData = [
  { name: 'Completed', value: 28, color: '#2E7D32' },
  { name: 'In Progress', value: 12, color: '#FBC02D' },
  { name: 'Not Started', value: 60, color: '#bdbdbd' },
];

const ProgressChart = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('subject');
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('dashboard.progress')}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="subject" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="subject">{t('progress.bySubject')}</TabsTrigger>
            <TabsTrigger value="time">{t('progress.byTime')}</TabsTrigger>
            <TabsTrigger value="completion">Completion</TabsTrigger>
          </TabsList>
          
          <TabsContent value="subject" className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={subjectData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="score" name="Score (%)">
                    {subjectData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="time" className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={timeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="hours" 
                    name="Hours Spent" 
                    stroke="#0288D1" 
                    strokeWidth={2} 
                    activeDot={{ r: 6 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="completion" className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ProgressChart;
