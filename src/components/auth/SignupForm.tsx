
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/components/ui/use-toast";
import { Eye, EyeOff } from 'lucide-react';

interface SignupFormProps {
  onSuccess: () => void;
}

type UserType = 'student' | 'teacher' | 'parent';

const SignupForm = ({ onSuccess }: SignupFormProps) => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [userType, setUserType] = useState<UserType>('student');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name || !email || !password) {
      setError('Please fill in all required fields');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: t('auth.signupSuccess'),
        description: t('auth.accountCreated'),
        variant: "default",
      });
      onSuccess();
    }, 1500);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 pt-4">
      <div className="space-y-2">
        <Label htmlFor="name">{t('auth.name')}</Label>
        <Input
          id="name"
          type="text"
          placeholder={t('auth.namePlaceholder')}
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="signup-email">{t('auth.email')}</Label>
        <Input
          id="signup-email"
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="signup-password">{t('auth.password')}</Label>
        <div className="relative">
          <Input
            id="signup-password"
            type={showPassword ? "text" : "password"}
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
            onClick={() => setShowPassword(!showPassword)}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="signup-confirm-password">{t('auth.confirmPassword', 'Confirm Password')}</Label>
        <div className="relative">
          <Input
            id="signup-confirm-password"
            type={showConfirmPassword ? "text" : "password"}
            placeholder="••••••••"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            aria-label={showConfirmPassword ? "Hide password" : "Show password"}
          >
            {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label>{t('auth.userType')}</Label>
        <RadioGroup 
          value={userType} 
          onValueChange={(value) => setUserType(value as UserType)}
          className="grid grid-cols-3 gap-2 pt-2"
        >
          <div className="flex flex-col items-center space-y-2">
            <div className={`p-3 rounded-lg border-2 ${userType === 'student' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
              <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                <circle cx="100" cy="80" r="40" fill={userType === 'student' ? "#1B9655" : "#CADAD1"}/>
                <path d="M160 180C160 147.909 133.137 122 100 122C66.8629 122 40 147.909 40 180" stroke={userType === 'student' ? "#1B9655" : "#CADAD1"} strokeWidth="12"/>
              </svg>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="student" id="student" />
              <Label htmlFor="student" className="cursor-pointer">{t('auth.student')}</Label>
            </div>
          </div>
          
          <div className="flex flex-col items-center space-y-2">
            <div className={`p-3 rounded-lg border-2 ${userType === 'teacher' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
              <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                <circle cx="100" cy="65" r="35" fill={userType === 'teacher' ? "#1B9655" : "#CADAD1"}/>
                <path d="M150 180C150 152.386 127.614 130 100 130C72.3858 130 50 152.386 50 180" stroke={userType === 'teacher' ? "#1B9655" : "#CADAD1"} strokeWidth="12"/>
                <rect x="75" y="25" width="50" height="15" rx="2" fill={userType === 'teacher' ? "#1B9655" : "#CADAD1"}/>
              </svg>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="teacher" id="teacher" />
              <Label htmlFor="teacher" className="cursor-pointer">{t('auth.teacher')}</Label>
            </div>
          </div>
          
          <div className="flex flex-col items-center space-y-2">
            <div className={`p-3 rounded-lg border-2 ${userType === 'parent' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
              <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                <circle cx="70" cy="80" r="30" fill={userType === 'parent' ? "#EBA237" : "#CADAD1"}/>
                <circle cx="130" cy="80" r="30" fill={userType === 'parent' ? "#1E4B68" : "#CADAD1"}/>
                <path d="M40 180C40 147.909 53.8629 122 70 122C86.1371 122 100 147.909 100 180" stroke={userType === 'parent' ? "#EBA237" : "#CADAD1"} strokeWidth="10"/>
                <path d="M100 180C100 147.909 113.863 122 130 122C146.137 122 160 147.909 160 180" stroke={userType === 'parent' ? "#1E4B68" : "#CADAD1"} strokeWidth="10"/>
              </svg>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="parent" id="parent" />
              <Label htmlFor="parent" className="cursor-pointer">{t('auth.parent')}</Label>
            </div>
          </div>
        </RadioGroup>
      </div>
      
      {error && <p className="text-sm text-destructive">{error}</p>}
      
      <Button 
        type="submit" 
        variant="brand" 
        className="w-full" 
        disabled={isLoading}
      >
        {isLoading ? t('auth.creatingAccount') : t('auth.signup')}
      </Button>
      
      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t"></div>
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            {t('auth.or')}
          </span>
        </div>
      </div>
      
      <Button type="button" variant="outline" className="w-full">
        <svg
          className="mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 48 48"
        >
          <path
            fill="#FFC107"
            d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
          />
          <path
            fill="#FF3D00"
            d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
          />
          <path
            fill="#4CAF50"
            d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
          />
          <path
            fill="#1976D2"
            d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
          />
        </svg>
        {t('auth.withGoogle')}
      </Button>
    </form>
  );
};

export default SignupForm;
