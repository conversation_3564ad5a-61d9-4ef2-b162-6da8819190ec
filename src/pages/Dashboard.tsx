import { useState } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Layout from "@/components/layout/Layout";
import CourseCard from "@/components/shared/CourseCard";
import ProgressChart from "@/components/shared/ProgressChart";
import { useAppContext } from "@/contexts/AppContext";
import { useAuth } from "@/hooks/useAuth";
import { Mic } from "lucide-react";

const Dashboard = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { downloadedCourses, courses, activateVoiceAssistant } =
    useAppContext();
  const [activeTab, setActiveTab] = useState("all");

  // Get user's first name
  const userName = user?.first_name || user?.name?.split(" ")[0] || "User";

  // Recent activity data (mock)
  const recentActivities = [
    {
      id: "1",
      type: "course_progress",
      title: "Introduction to Mathematics",
      date: new Date(2023, 4, 15),
      details: "Completed Module 3: Addition and Subtraction",
    },
    {
      id: "2",
      type: "download",
      title: "English Grammar",
      date: new Date(2023, 4, 14),
      details: "Downloaded course for offline use",
    },
    {
      id: "3",
      type: "achievement",
      title: "First Quiz Completed",
      date: new Date(2023, 4, 12),
      details: "Scored 85% on Mathematics Quiz",
    },
    {
      id: "4",
      type: "voice",
      title: "Voice Assistant",
      date: new Date(2023, 4, 10),
      details: "Asked 5 questions about photosynthesis",
    },
  ];

  const handleActivateVoiceAssistant = () => {
    activateVoiceAssistant();
  };

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Welcome back, {userName}!
            </h1>
            <p className="text-muted-foreground">
              {t(
                "dashboard.summary",
                "Here's your learning summary and recent activities"
              )}
            </p>
          </div>

          <Button size="lg" onClick={handleActivateVoiceAssistant}>
            <Mic className="mr-2 h-4 w-4" />
            {t("dashboard.ask", "Ask KoGidi")}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>
                  {t("dashboard.continue", "Continue Learning")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs
                  defaultValue="all"
                  value={activeTab}
                  onValueChange={setActiveTab}
                >
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="all">
                      {t("courses.all", "All")}
                    </TabsTrigger>
                    <TabsTrigger value="downloaded">
                      {t("courses.downloaded", "Downloaded")}
                    </TabsTrigger>
                    <TabsTrigger value="inProgress">
                      {t("courses.inProgress", "In Progress")}
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="all">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {courses.slice(0, 4).map((course) => (
                        <CourseCard key={course.id} {...course} />
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="downloaded">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {downloadedCourses.length > 0 ? (
                        downloadedCourses.map((course) => (
                          <CourseCard key={course.id} {...course} />
                        ))
                      ) : (
                        <p className="col-span-2 text-center py-8 text-muted-foreground">
                          {t(
                            "dashboard.noDownloaded",
                            "No downloaded courses yet. Browse the course catalog to download some."
                          )}
                        </p>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="inProgress">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {courses.filter((course) => course.progress > 0).length >
                      0 ? (
                        courses
                          .filter((course) => course.progress > 0)
                          .map((course) => (
                            <CourseCard key={course.id} {...course} />
                          ))
                      ) : (
                        <p className="col-span-2 text-center py-8 text-muted-foreground">
                          {t(
                            "dashboard.noStarted",
                            "You haven't started any courses yet."
                          )}
                        </p>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <ProgressChart />
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>
                  {t("dashboard.activity", "Recent Activity")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-5">
                  {recentActivities.map((activity) => (
                    <div
                      key={activity.id}
                      className="border-b pb-4 last:border-0"
                    >
                      <div className="flex justify-between items-start mb-1">
                        <h4 className="font-medium">{activity.title}</h4>
                        <span className="text-xs text-muted-foreground">
                          {activity.date.toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {activity.details}
                      </p>
                    </div>
                  ))}
                </div>

                {recentActivities.length === 0 && (
                  <p className="text-center py-8 text-muted-foreground">
                    {t(
                      "dashboard.noActivities",
                      "No recent activities to display."
                    )}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
