
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Layout from '@/components/layout/Layout';
import CourseCard from '@/components/shared/CourseCard';
import { useAppContext } from '@/contexts/AppContext';
import { useAuth } from '@/hooks/useAuth';

const Courses = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const { courses } = useAppContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCourses, setFilteredCourses] = useState(courses);
  const [filterLanguage, setFilterLanguage] = useState('all');
  const [filterGrade, setFilterGrade] = useState('all');
  const [filterSubject, setFilterSubject] = useState('all');
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto py-8 flex justify-center items-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-kogidi-green"></div>
        </div>
      </Layout>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <Layout>
        <div className="container mx-auto py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Access Required</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                Please log in to access our courses and start your learning journey.
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button 
                  variant="brand" 
                  onClick={() => navigate('/login')}
                  className="flex-1"
                >
                  Log In
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/signup')}
                  className="flex-1"
                >
                  Sign Up
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }
  
  // Extract unique values for filters
  const languages = Array.from(new Set(courses.map(course => course.language)));
  const grades = Array.from(new Set(courses.map(course => course.grade)));
  const subjects = Array.from(new Set(courses.map(course => course.subject)));
  
  // Apply filters and search
  useEffect(() => {
    let filtered = courses;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(course => 
        course.title.toLowerCase().includes(query) ||
        course.description.toLowerCase().includes(query)
      );
    }
    
    // Apply language filter
    if (filterLanguage !== 'all') {
      filtered = filtered.filter(course => course.language === filterLanguage);
    }
    
    // Apply grade filter
    if (filterGrade !== 'all') {
      filtered = filtered.filter(course => course.grade === filterGrade);
    }
    
    // Apply subject filter
    if (filterSubject !== 'all') {
      filtered = filtered.filter(course => course.subject === filterSubject);
    }
    
    setFilteredCourses(filtered);
  }, [searchQuery, filterLanguage, filterGrade, filterSubject, courses]);
  
  // Reset all filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setFilterLanguage('all');
    setFilterGrade('all');
    setFilterSubject('all');
  };
  
  return (
    <Layout>
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 text-primary-900">{t('nav.courses')}</h1>
          <p className="text-gray-600">Explore our wide range of courses for all grade levels</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            <div className="lg:col-span-2">
              <Input
                placeholder={t('courses.search')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            
            <div>
              <Select value={filterLanguage} onValueChange={setFilterLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder={t('courses.language')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Languages</SelectItem>
                  {languages.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang === 'en' ? 'English' : 
                       lang === 'yo' ? 'Yorùbá' : 
                       lang === 'ig' ? 'Igbo' : 
                       lang === 'ha' ? 'Hausa' : lang}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Select value={filterGrade} onValueChange={setFilterGrade}>
                <SelectTrigger>
                  <SelectValue placeholder={t('courses.grade')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  {grades.map((grade) => (
                    <SelectItem key={grade} value={grade}>
                      {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Select value={filterSubject} onValueChange={setFilterSubject}>
                <SelectTrigger>
                  <SelectValue placeholder={t('courses.subject')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button variant="ghost" size="sm" onClick={handleResetFilters}>
              Reset Filters
            </Button>
          </div>
        </div>
        
        {filteredCourses.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">No courses found that match your filters.</p>
            <Button variant="link" onClick={handleResetFilters}>
              Clear all filters
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredCourses.map((course) => (
              <CourseCard key={course.id} {...course} />
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Courses;
