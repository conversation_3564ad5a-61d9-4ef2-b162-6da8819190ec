
import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import Layout from '@/components/layout/Layout';
import { useAppContext } from '@/contexts/AppContext';
import { Mic } from 'lucide-react';

const LessonDetail = () => {
  const { t } = useTranslation();
  const { courseId } = useParams();
  const { courses, activateVoiceAssistant } = useAppContext();
  const [showTranscript, setShowTranscript] = useState(false);
  
  // Find course by ID
  const course = courses.find(c => c.id === courseId);
  
  // Mock lesson data
  const lessons = [
    {
      id: '1',
      title: 'Introduction',
      duration: '10 min',
      type: 'video',
      completed: true
    },
    {
      id: '2',
      title: 'Basic Concepts',
      duration: '15 min',
      type: 'video',
      completed: false
    },
    {
      id: '3',
      title: 'Practice Exercise',
      duration: '20 min',
      type: 'quiz',
      completed: false
    },
    {
      id: '4',
      title: 'Advanced Topics',
      duration: '18 min',
      type: 'video',
      completed: false
    },
    {
      id: '5',
      title: 'Final Quiz',
      duration: '25 min',
      type: 'quiz',
      completed: false
    }
  ];
  
  // Mock transcript text
  const transcriptText = `
    Welcome to this lesson on ${course?.title}. In this video, we'll cover the foundational concepts
    that are essential for understanding this subject.
    
    First, let's start with the definition...
    
    [Continue transcript text here]
    
    Thank you for watching this introduction. In the next video, we'll dive deeper into specific examples and applications.
  `;
  
  const handleActivateVoiceAssistant = () => {
    activateVoiceAssistant();
  };
  
  if (!course) {
    return (
      <Layout>
        <div className="container mx-auto py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Course not found</h1>
          <p className="mb-8">The course you're looking for doesn't exist or hasn't been downloaded yet.</p>
          <Button asChild>
            <Link to="/courses">Browse Courses</Link>
          </Button>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Link to="/courses" className="hover:text-primary">Courses</Link>
            <span className="mx-2">/</span>
            <span>{course.title}</span>
          </div>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h1 className="text-3xl font-bold text-primary-900">{course.title}</h1>
            <Button onClick={handleActivateVoiceAssistant}>
              <Mic className="mr-2 h-4 w-4" />
              {t('dashboard.ask')}
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h2 className="font-semibold text-lg mb-4">{t('lesson.contents')}</h2>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="module-1">
                  <AccordionTrigger>Module 1: Basics</AccordionTrigger>
                  <AccordionContent>
                    <ul className="space-y-2 pl-2">
                      {lessons.map((lesson) => (
                        <li key={lesson.id} className="border-l-2 pl-3 py-1 text-sm border-gray-200 hover:border-primary">
                          <div className="flex items-center justify-between">
                            <div>
                              <Link to="#" className="hover:text-primary">
                                {lesson.title}
                              </Link>
                              <div className="text-xs text-muted-foreground">
                                {lesson.duration} • {lesson.type}
                              </div>
                            </div>
                            {lesson.completed && (
                              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="module-2">
                  <AccordionTrigger>Module 2: Intermediate</AccordionTrigger>
                  <AccordionContent>
                    <p className="text-sm text-gray-600 p-2">
                      Complete Module 1 to unlock this content.
                    </p>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="module-3">
                  <AccordionTrigger>Module 3: Advanced</AccordionTrigger>
                  <AccordionContent>
                    <p className="text-sm text-gray-600 p-2">
                      Complete Module 2 to unlock this content.
                    </p>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
          
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              <div className="aspect-video bg-gray-900 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button variant="outline" size="lg" className="bg-white/10 hover:bg-white/20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-6 w-6"
                    >
                      <polygon points="5 3 19 12 5 21 5 3" />
                    </svg>
                  </Button>
                </div>
                <img 
                  src={course.thumbnail} 
                  alt={course.title} 
                  className="w-full h-full object-cover opacity-50" 
                />
              </div>
            </div>
            
            <Tabs defaultValue="content">
              <TabsList>
                <TabsTrigger value="content">Lesson Content</TabsTrigger>
                <TabsTrigger value="transcript">Transcript</TabsTrigger>
                <TabsTrigger value="notes">Notes</TabsTrigger>
              </TabsList>
              <TabsContent value="content" className="p-4">
                <h2 className="text-xl font-semibold mb-4">{lessons[0].title}</h2>
                <p className="mb-4">
                  This introductory lesson covers the fundamental concepts of {course.title}. 
                  Watch the video above to learn more about this topic.
                </p>
                <h3 className="text-lg font-medium mb-2">Key Learning Points:</h3>
                <ul className="list-disc pl-6 mb-4 space-y-1">
                  <li>Understanding the basic principles</li>
                  <li>Exploring practical examples</li>
                  <li>Learning about applications in real-world contexts</li>
                  <li>Preparing for the practice exercises</li>
                </ul>
                <p className="mb-4">
                  After watching this lesson, you should be able to explain the core concepts and 
                  be ready to move on to the more advanced topics in the next modules.
                </p>
                <div className="mt-8 flex gap-4">
                  <Button disabled>Previous Lesson</Button>
                  <Button>Next Lesson</Button>
                </div>
              </TabsContent>
              <TabsContent value="transcript" className="p-4">
                <div className="bg-gray-50 p-4 rounded">
                  <p className="whitespace-pre-line">{transcriptText}</p>
                </div>
              </TabsContent>
              <TabsContent value="notes" className="p-4">
                <div className="border rounded p-4">
                  <p className="text-muted-foreground mb-4">
                    You haven't added any notes for this lesson yet.
                  </p>
                  <textarea 
                    className="w-full p-3 border rounded min-h-[200px]" 
                    placeholder="Type your notes here..."
                  ></textarea>
                  <div className="mt-4">
                    <Button>Save Notes</Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LessonDetail;
