import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import FeatureCard from "@/components/landing/FeatureCard";
import UserStory from "@/components/landing/UserStory";
import BenefitsList from "@/components/landing/BenefitsList";
import { useAuth } from "@/hooks/useAuth";

// Import SVG assets
import voiceAssistantSvg from "@/assets/voice-assistant.svg";
import offlineLearning from "@/assets/offline-learning.svg";
import progressTracking from "@/assets/progress-tracking.svg";
import student1 from "@/assets/student1.svg";
import student2 from "@/assets/student2.svg";
import teacher from "@/assets/teacher.svg";
import parent from "@/assets/parent.svg";

const Landing = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const [email, setEmail] = useState("");

  // Redirect logged-in users to dashboard
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-kogidi-green"></div>
      </div>
    );
  }

  // Don't render if authenticated (will redirect)
  if (isAuthenticated) {
    return null;
  }

  const handleGetStarted = () => {
    navigate("/signup");
  };

  const handleLearnMore = () => {
    const howItWorksSection = document.getElementById("how-it-works");
    if (howItWorksSection) {
      howItWorksSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Newsletter signup:", email);
    setEmail("");
  };

  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" },
  };

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <Layout hideFooter={false}>
      <div className="relative">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden bg-gradient-to-br from-kogidi-green/5 via-background to-kogidi-teal/5">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-4xl mx-auto text-center"
              initial="initial"
              animate="animate"
              variants={staggerChildren}
            >
              <motion.div variants={fadeInUp}>
                <Badge
                  variant="secondary"
                  className="mb-6 text-sm px-4 py-2 bg-kogidi-green/10 text-kogidi-green border-kogidi-green/20"
                >
                  {t("landing.hero.badge")}
                </Badge>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight"
                variants={fadeInUp}
              >
                {t("landing.hero.title")}
              </motion.h1>

              <motion.p
                className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed"
                variants={fadeInUp}
              >
                {t("landing.hero.subtitle")}
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 justify-center"
                variants={fadeInUp}
              >
                <Button
                  size="lg"
                  variant="brand"
                  className="px-8 py-3 text-lg font-medium"
                  onClick={handleGetStarted}
                >
                  {t("landing.hero.cta")}
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="px-8 py-3 text-lg font-medium border-kogidi-green text-kogidi-green hover:bg-kogidi-green hover:text-white"
                  onClick={handleLearnMore}
                >
                  {t("landing.hero.secondaryCta")}
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* About Section */}
        <section className="py-20 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-6xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.div className="text-center mb-16" variants={fadeInUp}>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {t("landing.about.title")}
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  {t("landing.about.description")}
                </p>
              </motion.div>

              <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
                <motion.div variants={fadeInUp}>
                  <h3 className="text-2xl font-bold text-foreground mb-4">
                    {t("landing.about.nigerianEducation")}
                  </h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {t("landing.about.nigerianEducationText")}
                  </p>
                  <h3 className="text-2xl font-bold text-foreground mb-4">
                    {t("landing.about.simplifiedEnglish")}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {t("landing.about.simplifiedEnglishText")}
                  </p>
                </motion.div>

                <motion.div variants={fadeInUp}>
                  <Card className="p-8 bg-gradient-to-br from-kogidi-green/5 to-kogidi-teal/5 border-kogidi-green/20">
                    <CardContent className="p-0">
                      <div className="grid grid-cols-3 gap-6 text-center">
                        <div>
                          <div className="text-3xl font-bold text-kogidi-green mb-2">
                            10K+
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {t("landing.about.students")}
                          </div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-kogidi-green mb-2">
                            500+
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {t("landing.about.teachers")}
                          </div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-kogidi-green mb-2">
                            200+
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {t("landing.about.schools")}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="mt-8 text-center">
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-kogidi-green text-kogidi-green hover:bg-kogidi-green hover:text-white"
                    >
                      {t("landing.about.watchVideo")}
                    </Button>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gradient-to-br from-kogidi-teal/5 to-kogidi-green/5">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-6xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.div className="text-center mb-16" variants={fadeInUp}>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {t("landing.features.title")}
                </h2>
                <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                  {t("landing.features.subtitle")}
                </p>
              </motion.div>

              <motion.div
                className="grid md:grid-cols-3 gap-8"
                variants={staggerChildren}
              >
                <motion.div variants={fadeInUp}>
                  <FeatureCard
                    icon={offlineLearning}
                    title={t("landing.features.offline.title")}
                    description={t("landing.features.offline.description")}
                  />
                </motion.div>
                <motion.div variants={fadeInUp}>
                  <FeatureCard
                    icon={voiceAssistantSvg}
                    title={t("landing.features.voice.title")}
                    description={t("landing.features.voice.description")}
                  />
                </motion.div>
                <motion.div variants={fadeInUp}>
                  <FeatureCard
                    icon={progressTracking}
                    title={t("landing.features.progress.title")}
                    description={t("landing.features.progress.description")}
                  />
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* User Stories Section */}
        <section className="py-20 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-6xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.div className="text-center mb-16" variants={fadeInUp}>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {t("landing.stories.title")}
                </h2>
                <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                  {t("landing.stories.subtitle")}
                </p>
              </motion.div>

              <Tabs
                defaultValue="student"
                className="w-full"
                orientation="horizontal"
              >
                <TabsList className="grid w-full grid-cols-3 mb-12 bg-muted h-12">
                  <TabsTrigger
                    value="student"
                    className="text-lg font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    {t("landing.stories.student")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="teacher"
                    className="text-lg font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    {t("landing.stories.teacher")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="parent"
                    className="text-lg font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    {t("landing.stories.parent")}
                  </TabsTrigger>
                </TabsList>

                <TabsContent
                  value="student"
                  className="mt-8 block opacity-100 data-[state=inactive]:hidden"
                >
                  <motion.div
                    key="student-content"
                    className="grid md:grid-cols-2 gap-8"
                    initial="initial"
                    animate="animate"
                    variants={staggerChildren}
                  >
                    <motion.div variants={fadeInUp}>
                      <UserStory
                        image={
                          <img
                            src={student1}
                            alt="Student 1"
                            className="w-full h-full rounded-full object-cover"
                          />
                        }
                        name="Adunni Okafor"
                        role={t("landing.stories.student")}
                        location="Lagos, Nigeria"
                        quote={t("landing.stories.student1Quote")}
                        benefit={t("landing.stories.student1Benefit")}
                      />
                    </motion.div>
                    <motion.div variants={fadeInUp}>
                      <UserStory
                        image={
                          <img
                            src={student2}
                            alt="Student 2"
                            className="w-full h-full rounded-full object-cover"
                          />
                        }
                        name="Kemi Adebayo"
                        role={t("landing.stories.student")}
                        location="Kano, Nigeria"
                        quote={t("landing.stories.student2Quote")}
                        benefit={t("landing.stories.student2Benefit")}
                      />
                    </motion.div>
                  </motion.div>
                </TabsContent>

                <TabsContent
                  value="teacher"
                  className="mt-8 block opacity-100 data-[state=inactive]:hidden"
                >
                  <motion.div
                    key="teacher-content"
                    className="max-w-2xl mx-auto"
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                  >
                    <UserStory
                      image={
                        <img
                          src={teacher}
                          alt="Teacher"
                          className="w-full h-full rounded-full object-cover"
                        />
                      }
                      name="Mr. Chukwu Okonkwo"
                      role={t("landing.stories.teacher")}
                      location="Abuja, Nigeria"
                      quote={t("landing.stories.teacherQuote")}
                      benefit={t("landing.stories.teacherBenefit")}
                    />
                  </motion.div>
                </TabsContent>

                <TabsContent
                  value="parent"
                  className="mt-8 block opacity-100 data-[state=inactive]:hidden"
                >
                  <motion.div
                    key="parent-content"
                    className="max-w-2xl mx-auto"
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                  >
                    <UserStory
                      image={
                        <img
                          src={parent}
                          alt="Parent"
                          className="w-full h-full rounded-full object-cover"
                        />
                      }
                      name="Mrs. Fatima Ibrahim"
                      role={t("landing.stories.parent")}
                      location="Katsina, Nigeria"
                      quote={t("landing.stories.parentQuote")}
                      benefit={t("landing.stories.parentBenefit")}
                    />
                  </motion.div>
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 bg-gradient-to-br from-kogidi-green/5 to-background">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-4xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <BenefitsList />
            </motion.div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-6xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.div className="text-center mb-16" variants={fadeInUp}>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {t("landing.howItWorks.title")}
                </h2>
                <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                  {t("landing.howItWorks.subtitle")}
                </p>
              </motion.div>

              <motion.div
                className="grid md:grid-cols-3 gap-8"
                variants={staggerChildren}
              >
                <motion.div className="text-center p-6" variants={fadeInUp}>
                  <div className="w-16 h-16 bg-kogidi-green text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    1
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-3">
                    {t("landing.howItWorks.step1Title")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t("landing.howItWorks.step1Text")}
                  </p>
                </motion.div>

                <motion.div className="text-center p-6" variants={fadeInUp}>
                  <div className="w-16 h-16 bg-kogidi-teal text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    2
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-3">
                    {t("landing.howItWorks.step2Title")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t("landing.howItWorks.step2Text")}
                  </p>
                </motion.div>

                <motion.div className="text-center p-6" variants={fadeInUp}>
                  <div className="w-16 h-16 bg-kogidi-green text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    3
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-3">
                    {t("landing.howItWorks.step3Title")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t("landing.howItWorks.step3Text")}
                  </p>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-kogidi-green to-kogidi-teal text-white">
          <div className="container mx-auto px-4">
            <motion.div
              className="max-w-4xl mx-auto text-center"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                {t("landing.cta.title")}
              </h2>
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                {t("landing.cta.subtitle")}
              </p>
              <Button
                size="lg"
                variant="secondary"
                className="px-8 py-3 text-lg font-medium bg-white text-kogidi-green hover:bg-gray-100"
                onClick={handleGetStarted}
              >
                {t("landing.cta.buttonText")}
              </Button>
            </motion.div>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default Landing;
