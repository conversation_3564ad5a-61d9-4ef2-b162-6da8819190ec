
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress as ProgressBar } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import Layout from '@/components/layout/Layout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/hooks/useAuth';
import { useAppContext } from '@/contexts/AppContext';
import api from '@/services/apiClient';
import { BookOpen, Trophy, Clock, TrendingUp, Download } from 'lucide-react';

interface CourseProgress {
  id: string;
  title: string;
  subject: string;
  grade: string;
  progress: number;
  totalLessons: number;
  completedLessons: number;
  timeSpent: number; // in minutes
  lastAccessed: string;
  achievements: string[];
}

interface ProgressStats {
  totalCourses: number;
  completedCourses: number;
  totalTimeSpent: number;
  averageProgress: number;
  achievements: number;
  streak: number;
}

const Progress = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { courses } = useAppContext();
  const [isLoading, setIsLoading] = useState(true);
  const [progressData, setProgressData] = useState<CourseProgress[]>([]);
  const [stats, setStats] = useState<ProgressStats>({
    totalCourses: 0,
    completedCourses: 0,
    totalTimeSpent: 0,
    averageProgress: 0,
    achievements: 0,
    streak: 0,
  });

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadProgressData = async () => {
      try {
        setIsLoading(true);
        
        // Mock progress data based on available courses
        const mockProgress: CourseProgress[] = courses.map((course, index) => ({
          id: course.id,
          title: course.title,
          subject: course.subject,
          grade: course.grade,
          progress: Math.floor(Math.random() * 100),
          totalLessons: Math.floor(Math.random() * 20) + 5,
          completedLessons: Math.floor(Math.random() * 15),
          timeSpent: Math.floor(Math.random() * 300) + 30,
          lastAccessed: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          achievements: ['First Lesson', 'Quick Learner'].slice(0, Math.floor(Math.random() * 3)),
        }));

        setProgressData(mockProgress);

        // Calculate stats
        const totalProgress = mockProgress.reduce((sum, course) => sum + course.progress, 0);
        const totalTime = mockProgress.reduce((sum, course) => sum + course.timeSpent, 0);
        const completed = mockProgress.filter(course => course.progress >= 100).length;

        setStats({
          totalCourses: mockProgress.length,
          completedCourses: completed,
          totalTimeSpent: totalTime,
          averageProgress: mockProgress.length > 0 ? Math.round(totalProgress / mockProgress.length) : 0,
          achievements: mockProgress.reduce((sum, course) => sum + course.achievements.length, 0),
          streak: Math.floor(Math.random() * 30) + 1,
        });

        // In a real app, you would call:
        // const response = await api.get('/api/v1/progress/');
        // setProgressData(response.data.courses);
        // setStats(response.data.stats);
        
      } catch (error) {
        console.error('Failed to load progress data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProgressData();
  }, [courses]);

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const exportProgress = async () => {
    try {
      // In a real app, this would generate and download a PDF report
      // const response = await api.get('/api/v1/progress/export/', { responseType: 'blob' });
      // const blob = new Blob([response.data], { type: 'application/pdf' });
      // const url = window.URL.createObjectURL(blob);
      // const link = document.createElement('a');
      // link.href = url;
      // link.download = 'progress-report.pdf';
      // link.click();
      
      // For now, just show a toast
      console.log('Exporting progress report...');
    } catch (error) {
      console.error('Failed to export progress:', error);
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="container mx-auto py-8 flex justify-center items-center min-h-[50vh]">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-kogidi-green"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="container mx-auto py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2 text-primary-900">Learning Progress</h1>
              <p className="text-gray-600">Track your learning journey and achievements</p>
            </div>
            <Button variant="outline" onClick={exportProgress}>
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <BookOpen className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Courses Enrolled</p>
                    <p className="text-2xl font-bold">{stats.totalCourses}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Trophy className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Completed</p>
                    <p className="text-2xl font-bold">{stats.completedCourses}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Clock className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Time Spent</p>
                    <p className="text-2xl font-bold">{formatTime(stats.totalTimeSpent)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Avg. Progress</p>
                    <p className="text-2xl font-bold">{stats.averageProgress}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress Details */}
          <Tabs defaultValue="courses" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="courses">Course Progress</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="courses" className="mt-6">
              <div className="grid gap-6">
                {progressData.map((course) => (
                  <Card key={course.id}>
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{course.title}</CardTitle>
                          <p className="text-sm text-gray-600">{course.subject} • {course.grade}</p>
                        </div>
                        <Badge variant={course.progress >= 100 ? "default" : "secondary"}>
                          {course.progress >= 100 ? "Completed" : "In Progress"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span>Progress</span>
                            <span>{course.progress}%</span>
                          </div>
                          <ProgressBar value={course.progress} className="h-2" />
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Lessons</p>
                            <p className="font-medium">{course.completedLessons}/{course.totalLessons}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Time Spent</p>
                            <p className="font-medium">{formatTime(course.timeSpent)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Last Accessed</p>
                            <p className="font-medium">{new Date(course.lastAccessed).toLocaleDateString()}</p>
                          </div>
                        </div>

                        {course.achievements.length > 0 && (
                          <div>
                            <p className="text-sm text-gray-600 mb-2">Achievements</p>
                            <div className="flex gap-2 flex-wrap">
                              {course.achievements.map((achievement, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {achievement}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="achievements" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Your Achievements</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                      <h3 className="font-medium">First Course Completed</h3>
                      <p className="text-sm text-gray-600">Completed your first course</p>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <Trophy className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                      <h3 className="font-medium">7-Day Streak</h3>
                      <p className="text-sm text-gray-600">Learned for 7 days in a row</p>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <Trophy className="h-8 w-8 text-green-500 mx-auto mb-2" />
                      <h3 className="font-medium">Quick Learner</h3>
                      <p className="text-sm text-gray-600">Completed lessons quickly</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Learning Streak</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold text-kogidi-green mb-2">{stats.streak}</div>
                      <p className="text-gray-600">Days in a row</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Subject Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span>Mathematics</span>
                        <span className="text-green-600 font-medium">85%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>English</span>
                        <span className="text-blue-600 font-medium">92%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Science</span>
                        <span className="text-purple-600 font-medium">78%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Layout>
    </ProtectedRoute>
  );
};

export default Progress;
