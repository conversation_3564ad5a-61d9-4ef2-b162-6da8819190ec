
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import Layout from '@/components/layout/Layout';
import { motion } from 'framer-motion';
import { Eye, EyeOff } from 'lucide-react';

// Define form schema with Zod
const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  userType: z.enum(['student', 'teacher', 'parent'] as const),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

const Login = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Get the path they were trying to access
  const from = (location.state as any)?.from || '/dashboard';

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      userType: 'student',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    
    try {
      await login({
        email: data.email,
        password: data.password,
        userType: data.userType
      });
      // Redirect to the page they were trying to access or dashboard
      navigate(from, { replace: true });
    } catch (error) {
      console.error('Login failed:', error);
      // Error is handled by useAuth hook
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="container max-w-md mx-auto py-12">
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="space-y-2 text-center">
            <h1 className="text-3xl font-bold">{t('auth.login', 'Login')}</h1>
            <p className="text-muted-foreground">{t('auth.loginDescription', 'Enter your credentials to access your account')}</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('auth.email', 'Email')}</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>{t('auth.password', 'Password')}</FormLabel>
                      <Link 
                        to="/forgot-password" 
                        className="text-sm text-primary hover:underline"
                      >
                        {t('auth.forgot', 'Forgot Password?')}
                      </Link>
                    </div>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          placeholder="••••••••" 
                          type={showPassword ? "text" : "password"}
                          {...field} 
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="userType"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>{t('auth.userType', 'I am a')}</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-3 gap-4"
                      >
                        <FormItem className="flex flex-col items-center space-y-2">
                          <div className={`p-3 rounded-lg border-2 ${field.value === 'student' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
                            <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                              <circle cx="100" cy="80" r="40" fill={field.value === 'student' ? "#1B9655" : "#CADAD1"}/>
                              <path d="M160 180C160 147.909 133.137 122 100 122C66.8629 122 40 147.909 40 180" stroke={field.value === 'student' ? "#1B9655" : "#CADAD1"} strokeWidth="12"/>
                            </svg>
                          </div>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="student" id="student" />
                              <FormLabel htmlFor="student" className="cursor-pointer">{t('auth.student', 'Student')}</FormLabel>
                            </div>
                          </FormControl>
                        </FormItem>
                        
                        <FormItem className="flex flex-col items-center space-y-2">
                          <div className={`p-3 rounded-lg border-2 ${field.value === 'teacher' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
                            <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                              <circle cx="100" cy="65" r="35" fill={field.value === 'teacher' ? "#1B9655" : "#CADAD1"}/>
                              <path d="M150 180C150 152.386 127.614 130 100 130C72.3858 130 50 152.386 50 180" stroke={field.value === 'teacher' ? "#1B9655" : "#CADAD1"} strokeWidth="12"/>
                              <rect x="75" y="25" width="50" height="15" rx="2" fill={field.value === 'teacher' ? "#1B9655" : "#CADAD1"}/>
                            </svg>
                          </div>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="teacher" id="teacher" />
                              <FormLabel htmlFor="teacher" className="cursor-pointer">{t('auth.teacher', 'Teacher')}</FormLabel>
                            </div>
                          </FormControl>
                        </FormItem>
                        
                        <FormItem className="flex flex-col items-center space-y-2">
                          <div className={`p-3 rounded-lg border-2 ${field.value === 'parent' ? 'border-kogidi-green bg-kogidi-green/10' : 'border-muted'}`}>
                            <svg width="40" height="40" viewBox="0 0 200 200" className="mx-auto">
                              <circle cx="70" cy="80" r="30" fill={field.value === 'parent' ? "#EBA237" : "#CADAD1"}/>
                              <circle cx="130" cy="80" r="30" fill={field.value === 'parent' ? "#1E4B68" : "#CADAD1"}/>
                              <path d="M40 180C40 147.909 53.8629 122 70 122C86.1371 122 100 147.909 100 180" stroke={field.value === 'parent' ? "#EBA237" : "#CADAD1"} strokeWidth="10"/>
                              <path d="M100 180C100 147.909 113.863 122 130 122C146.137 122 160 147.909 160 180" stroke={field.value === 'parent' ? "#1E4B68" : "#CADAD1"} strokeWidth="10"/>
                            </svg>
                          </div>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="parent" id="parent" />
                              <FormLabel htmlFor="parent" className="cursor-pointer">{t('auth.parent', 'Parent')}</FormLabel>
                            </div>
                          </FormControl>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>{t('auth.rememberMe', 'Remember me')}</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                variant="brand" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? t('auth.signingIn', 'Signing in...') : t('auth.login', 'Login')}
              </Button>
            </form>
          </Form>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                {t('auth.or', 'or')}
              </span>
            </div>
          </div>
          
          <Button variant="outline" className="w-full">
            <svg
              className="mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
            >
              <path
                fill="#FFC107"
                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
              />
              <path
                fill="#FF3D00"
                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
              />
              <path
                fill="#4CAF50"
                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
              />
              <path
                fill="#1976D2"
                d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
              />
            </svg>
            {t('auth.withGoogle', 'Sign in with Google')}
          </Button>
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              {t('auth.noAccount', "Don't have an account?")}{' '}
              <Link 
                to="/signup" 
                className="text-primary font-medium hover:underline"
              >
                {t('auth.createAccount', 'Create an account')}
              </Link>
            </p>
          </div>
        </motion.div>
      </div>
    </Layout>
  );
};

export default Login;
